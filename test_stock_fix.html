<!DOCTYPE html>
<html>
<head>
    <title>库存显示测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>库存显示修复测试</h1>
    
    <div id="test-results"></div>
    
    <script>
        // 模拟你的API响应数据
        const testData = {
            "id": 3,
            "name": "渠道1️⃣chongzhi",
            "card": 4,
            "inventory_hidden": 0,
            "delivery_way": 0,
            "shared": false
        };
        
        // 复制修复后的库存处理逻辑
        function inventoryHidden(state, count) {
            if (state == 0) {
                return count;
            }
            if (count <= 0) {
                return '已售罄';
            } else if (count <= 5) {
                return '马上卖完';
            } else if (count <= 20) {
                return '一般';
            } else if (count > 20) {
                return '充足';
            }
        }
        
        // 测试库存显示逻辑
        function testStockDisplay(item) {
            let stockDisplay = item.card !== undefined ? item.card : (item.stock !== undefined ? item.stock : 0);
            if (item.inventory_hidden !== undefined) {
                stockDisplay = inventoryHidden(item.inventory_hidden, stockDisplay);
            }
            return stockDisplay;
        }
        
        // 测试购买按钮逻辑
        function testBuyButton(item) {
            let actualStock = item.card !== undefined ? item.card : (item.card_count !== undefined ? item.card_count : 0);
            if (item.delivery_way == 0 && !item.shared && actualStock <= 0) {
                return '不可购买';
            }
            return '可以购买';
        }
        
        // 运行测试
        const stockResult = testStockDisplay(testData);
        const buyResult = testBuyButton(testData);
        
        document.getElementById('test-results').innerHTML = `
            <h2>测试结果：</h2>
            <p><strong>原始数据：</strong> ${JSON.stringify(testData)}</p>
            <p><strong>库存显示：</strong> ${stockResult}</p>
            <p><strong>购买状态：</strong> ${buyResult}</p>
            <p><strong>预期结果：</strong> 库存应该显示 "4"，购买状态应该是 "可以购买"</p>
            <p><strong>测试状态：</strong> ${stockResult == 4 && buyResult == '可以购买' ? '✅ 通过' : '❌ 失败'}</p>
        `;
    </script>
</body>
</html>
